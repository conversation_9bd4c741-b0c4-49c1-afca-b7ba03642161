import 'package:flutter/material.dart';
import 'dart:convert';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:nsl/l10n/app_localizations.dart';
import 'package:nsl/screens/new_design/my_library_mobile/solutions_library_mobile.dart';
import 'package:nsl/screens/new_design/my_library_mobile/objects_library_mobile.dart';
import 'package:nsl/screens/new_design/my_library_mobile/create_book_mobile.dart';
import 'package:nsl/screens/new_design/my_library_mobile/add_modules_mobileview.dart';
import 'package:nsl/widgets/common/nsl_knowledge_loader.dart';
import 'package:nsl/widgets/mobile/custom_drawer.dart';
import 'package:nsl/widgets/mobile/mobile_nav_item.dart';

class BookMobile {
  final String title;
  final String subtitle;
  final String imageUrl;
  final bool isDraft;
  final double imageWidth;
  final double imageHeight;

  BookMobile({
    required this.title,
    required this.subtitle,
    required this.imageUrl,
    this.isDraft = false,
    this.imageWidth = 101.0,
    this.imageHeight = 156.0,
  });

  factory BookMobile.fromJson(Map<String, dynamic> json) {
    return BookMobile(
      title: json['title'] as String,
      subtitle: json['subtitle'] as String,
      imageUrl: json['imageUrl'] as String,
      isDraft: json['isDraft'] as bool? ?? false,
      imageWidth: (json['imageWidth'] as num?)?.toDouble() ?? 101.0,
      imageHeight: (json['imageHeight'] as num?)?.toDouble() ?? 156.0,
    );
  }
}

class BooksLibraryMobile extends StatefulWidget {
  const BooksLibraryMobile({
    super.key,
    this.showNavigationBar = true,
  });

  final bool showNavigationBar;

  @override
  State<BooksLibraryMobile> createState() => _BooksLibraryMobileState();
}

class _BooksLibraryMobileState extends State<BooksLibraryMobile>
    with TickerProviderStateMixin {
  // Data
  late List<BookMobile> books;
  bool isLoading = true;

  // Navigation
  int selectedTabIndex = 0;

  // Carousel state
  int _currentIndex = 0;

  // Configuration constants
  static const double _booksPerView = 2.75;
  static const double _compactBooksPerView = 3.0;

  // Controllers
  late CarouselController _carouselController;
  final FocusNode _searchFocusNode = FocusNode();
  late AnimationController _loadingAnimationController;

  // Animations
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  // UI state
  bool _isKeyboardVisible = false;

  // JSON data
  static const String booksJsonString = '''
{
  "books": [
    {
      "title": "Ecommerce Platform Solutions",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book_01.png",
      "isDraft": false
    },
    {
      "title": "Fashion & Apparel Store",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-02.png",
      "isDraft": false
    },
    {
      "title": "Financial Advisory Platform",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-03.png",
      "isDraft": false
    },
    {
      "title": "Home Rentals App",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": true
    },
    {
      "title": "Online Grocery Store",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-05.png",
      "isDraft": false
    },
    {
      "title": "Courier & Logistics",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-06.png",
      "isDraft": false
    },
    {
      "title": "Automotive Marketplace",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-07.png",
      "isDraft": true
    },
    {
      "title": "Fitness & Wellness App",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-08.png",
      "isDraft": false
    },
    {
      "title": "Real Estate Platform",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-09.png",
      "isDraft": false
    },
    {
      "title": "Restaurant & Cafe",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false
    },
    {
      "title": "Healthcare Platform",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false
    },
    {
      "title": "Education Portal",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false
    },
    {
      "title": "Travel Booking App",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false
    },
    {
      "title": "Music Streaming",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false
    },
    {
      "title": "Social Media Platform",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false
    },
    {
      "title": "Gaming Platform",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false
    },
    {
      "title": "News & Media App",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false
    },
    {
      "title": "Banking App",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false
    },
    {
      "title": "Investment Platform",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false
    },
    {
      "title": "Delivery Service",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false
    },
    {
      "title": "Job Portal",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false
    },
    {
      "title": "Event Management",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false
    },
    {
      "title": "Video Streaming",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false
    },
    {
      "title": "Smart Home IoT",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false
    },
    {
      "title": "Cryptocurrency Exchange",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false
    }
  ]
}
''';

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    _initializeAnimations();
    _loadBooks();
  }

  void _initializeControllers() {
    _carouselController = CarouselController();
    _searchFocusNode.addListener(_onSearchFocusChange);
  }

  void _initializeAnimations() {
    _loadingAnimationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _loadingAnimationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _loadingAnimationController,
      curve: Curves.easeOutCubic,
    ));
  }

  void _onSearchFocusChange() {
    setState(() {
      // Trigger rebuild to check keyboard visibility in build method
    });
  }

  void _loadBooks() {
    try {
      final data = json.decode(booksJsonString);
      final loadedBooks = (data['books'] as List<dynamic>)
          .map((bookJson) =>
              BookMobile.fromJson(bookJson as Map<String, dynamic>))
          .toList();

      setState(() {
        books = loadedBooks;
        isLoading = false;
      });

      _loadingAnimationController.forward();
    } catch (e) {
      setState(() {
        books = <BookMobile>[];
        isLoading = false;
      });
      debugPrint('Error loading books: $e');
    }
  }

  @override
  void dispose() {
    _carouselController.dispose();
    _searchFocusNode.removeListener(_onSearchFocusChange);
    _searchFocusNode.dispose();
    _loadingAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    _updateKeyboardVisibility();
    return _buildBooksLibraryView();
  }

  void _updateKeyboardVisibility() {
    final keyboardHeight = MediaQuery.of(context).viewInsets.bottom;
    final isKeyboardVisible = keyboardHeight > 0;

    if (_isKeyboardVisible != isKeyboardVisible) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          setState(() {
            _isKeyboardVisible = isKeyboardVisible;
            _currentIndex = 0;
          });
        }
      });
    }
  }

  Widget _buildBooksLibraryView() {
    return Scaffold(
      backgroundColor: widget.showNavigationBar
          ? const Color(0xfff6f6f6)
          : Colors.transparent,
      drawer: widget.showNavigationBar ? const CustomDrawer() : null,
      appBar: widget.showNavigationBar ? _buildAppBar() : null,
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (widget.showNavigationBar) _buildTopNavigation(),
          if (widget.showNavigationBar) _buildSearchAndCreateSection(),
          _buildCustomSwiper(),
        ],
      ),
      floatingActionButton:
          widget.showNavigationBar ? _buildFloatingActionButton() : null,
    );
  }

  Widget _buildCustomSwiper() {
    return Expanded(
      child: NSLKnowledgeLoaderWrapper(
        isLoading: isLoading,
        child: books.isEmpty
            ? const Center(child: Text('No books found'))
            : Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
                // padding: const EdgeInsets.all(8.0),
                child: Column(
                  children: [
                    // CarouselView with individual books
                    SizedBox(
                      height: 250,
                      child: books.isEmpty
                          ? const Center(child: Text('No books found'))
                          : NotificationListener<ScrollNotification>(
                              onNotification: _onCarouselScroll,
                              child: CarouselView(
                                padding: EdgeInsets.zero,
                                backgroundColor: Colors.transparent,
                                controller: _carouselController,
                                itemExtent: _calculateBookItemExtent(),
                                enableSplash: false,
                                shape: const RoundedRectangleBorder(
                                    borderRadius: BorderRadius.zero),
                                shrinkExtent: _calculateBookItemExtent(),
                                children: books.asMap().entries.map((entry) {
                                  final bookIndex = entry.key;
                                  final book = entry.value;
                                  return _buildBookItem(book, bookIndex);
                                }).toList(),
                              ),
                            ),
                    ),
                    // Custom Dot Pagination (show dots for every few books)
                    if (books.length > 5)
                      _buildCustomPagination(_calculatePaginationCount()),
                  ],
                ),
              ),
      ),
    );
  }

  /// Builds individual book item widget
  Widget _buildBookItem(BookMobile book, int bookIndex) {
    return GestureDetector(
      onTap: () => _navigateToBookDetails(bookIndex),
      child: AnimatedBuilder(
        animation: _loadingAnimationController,
        builder: (context, child) {
          return FadeTransition(
            opacity: _fadeAnimation,
            child: SlideTransition(
              position: _slideAnimation,
              child: _buildBookContent(book),
            ),
          );
        },
      ),
    );
  }

  /// Builds the book content (cover, title, subtitle)
  Widget _buildBookContent(BookMobile book) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildBookCover(book),
        const SizedBox(height: 8),
        _buildBookTitle(book.title),
        const SizedBox(height: 4),
        _buildBookSubtitle(book.subtitle),
      ],
    );
  }

  /// Builds book cover with SVG background and draft badge
  Widget _buildBookCover(BookMobile book) {
    return Stack(
      clipBehavior: Clip.none,
      children: [
        // Background SVG shape
        Positioned(
          right: -8,
          bottom: 0,
          child: SvgPicture.asset(
            'assets/images/home-lib-shape.svg',
            width: 101.0 * 0.925,
            height: 156.0 * 0.95,
            fit: BoxFit.contain,
          ),
        ),
        // Main book image
        AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          width: 101.0,
          height: 156.0,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            image: DecorationImage(
              image: AssetImage(book.imageUrl),
              fit: BoxFit.cover,
            ),
          ),
        ),
        // Draft badge
        if (book.isDraft) _buildDraftBadge(),
      ],
    );
  }

  /// Builds draft badge for books
  Widget _buildDraftBadge() {
    return Positioned(
      top: 156.0 * 0.08,
      right: 101.0 * 0.14,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
        decoration: BoxDecoration(
          color: Colors.amber,
          borderRadius: BorderRadius.circular(4),
          boxShadow: [
            BoxShadow(
              color: Colors.amber.withValues(alpha: 0.3),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: const Text(
          'Draft',
          style: TextStyle(
            fontSize: 10,
            fontWeight: FontWeight.bold,
            color: Colors.black,
            fontFamily: "TiemposText",
          ),
        ),
      ),
    );
  }

  /// Builds book title
  Widget _buildBookTitle(String title) {
    return SizedBox(
      width: 101.0,
      child: Text(
        title,
        style: const TextStyle(
          fontWeight: FontWeight.w500,
          fontSize: 12,
          height: 1.334,
          color: Colors.black,
          fontFamily: "TiemposText",
        ),
        maxLines: 2,
        overflow: TextOverflow.ellipsis,
      ),
    );
  }

  /// Builds book subtitle
  Widget _buildBookSubtitle(String subtitle) {
    return SizedBox(
      width: 101.0,
      child: Text(
        subtitle,
        style: const TextStyle(
          fontWeight: FontWeight.w400,
          fontSize: 11,
          color: Colors.black,
          fontFamily: "TiemposText",
        ),
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
    );
  }

  /// Navigates to book details screen
  void _navigateToBookDetails(int bookIndex) {
    setState(() {
      _currentIndex = bookIndex;
    });
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const AddModulesMobileView(),
      ),
    );
  }

  // Calculate the width for each book item in carousel
  double _calculateBookItemExtent() {
    final screenWidth = MediaQuery.of(context).size.width;
    if (_isKeyboardVisible) {
      // Show 3 books when keyboard is visible
      return screenWidth / _compactBooksPerView;
    } else {
      // Show 2.5 books normally
      return screenWidth / _booksPerView;
    }
  }

  /// Calculate pagination count based on visible books
  int _calculatePaginationCount() {
    final booksPerView = _getBooksPerView();
    return (books.length / booksPerView).ceil();
  }

  /// Get current page index for pagination dots
  int _getCurrentPageIndex() {
    final booksPerView = _getBooksPerView();
    return (_currentIndex / booksPerView).floor();
  }

  /// Get books per view based on keyboard visibility
  double _getBooksPerView() {
    return _isKeyboardVisible ? _compactBooksPerView : _booksPerView;
  }

  /// Handles carousel scroll notifications to sync pagination
  bool _onCarouselScroll(ScrollNotification notification) {
    if (notification is ScrollUpdateNotification) {
      final scrollOffset = notification.metrics.pixels;
      final itemExtent = _calculateBookItemExtent();

      if (itemExtent > 0) {
        final newIndex = (scrollOffset / itemExtent).round();
        final clampedIndex = newIndex.clamp(0, books.length - 1);

        if (_currentIndex != clampedIndex) {
          setState(() {
            _currentIndex = clampedIndex;
          });
        }
      }
    }
    return false;
  }

  /// Builds custom pagination dots with proper CarouselView integration
  Widget _buildCustomPagination(int pageCount) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: List.generate(
          pageCount,
          (index) => _buildPaginationDot(index),
        ),
      ),
    );
  }

  /// Builds individual pagination dot
  Widget _buildPaginationDot(int index) {
    final isActive = _getCurrentPageIndex() == index;

    return GestureDetector(
      onTap: () => _onPaginationDotTap(index),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        margin: const EdgeInsets.symmetric(horizontal: 4),
        width: isActive ? 24 : 8,
        height: 8,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(4),
          color: isActive ? const Color(0xff0058FF) : Colors.grey.shade300,
          boxShadow: isActive
              ? [
                  BoxShadow(
                    color: const Color(0xff0058FF).withValues(alpha: 0.3),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ]
              : null,
        ),
      ),
    );
  }

  /// Handles pagination dot tap
  void _onPaginationDotTap(int pageIndex) {
    final booksPerView = _getBooksPerView();
    final targetBookIndex = (pageIndex * booksPerView).round();

    // Ensure target index is within bounds
    final clampedIndex = targetBookIndex.clamp(0, books.length - 1);

    _carouselController.animateTo(
      clampedIndex.toDouble(),
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );

    setState(() {
      _currentIndex = clampedIndex;
    });
  }

  /// Builds floating action button
  Widget _buildFloatingActionButton() {
    return SizedBox(
      width: 46,
      height: 46,
      child: FloatingActionButton(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const CreateBookMobile(),
            ),
          );
        },
        backgroundColor: const Color(0xff0058FF),
        foregroundColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(30),
        ),
        child: const Icon(Icons.add),
      ),
    );
  }

  /// Builds app bar for the screen
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: const Color(0xfff6f6f6),
      surfaceTintColor: Colors.transparent,
      foregroundColor: Colors.black,
      elevation: 0,
      automaticallyImplyLeading: false,
      titleSpacing: 0,
      title: Row(
        children: [
          Builder(
            builder: (context) => IconButton(
              icon: const Icon(Icons.menu, color: Colors.black, size: 24),
              onPressed: () => Scaffold.of(context).openDrawer(),
              padding: const EdgeInsets.symmetric(horizontal: 16),
            ),
          ),
          Expanded(
            child: Text(
              AppLocalizations.of(context).translate('library.pageTitle'),
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                fontFamily: 'TiemposText',
                color: Colors.black,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          const SizedBox(width: 56),
        ],
      ),
    );
  }

  /// Builds top navigation tabs
  Widget _buildTopNavigation() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          MobileNavItem(
            iconPath: 'assets/images/books-icon.svg',
            label: AppLocalizations.of(context).translate('library.books'),
            isActive: selectedTabIndex == 0,
            onTap: () {},
          ),
          MobileNavItem(
            iconPath: 'assets/images/square-box-uncheck.svg',
            label: AppLocalizations.of(context).translate('library.solutions'),
            isActive: selectedTabIndex == 1,
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const SolutionsLibraryMobile(),
                ),
              );
            },
          ),
          MobileNavItem(
            iconPath: 'assets/images/cube-box.svg',
            label: AppLocalizations.of(context).translate('library.objects'),
            isActive: selectedTabIndex == 2,
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const ObjectsLibraryMobile(),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  /// Builds search and filter section
  Widget _buildSearchAndCreateSection() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(
        children: [
          Container(
            height: 40,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(6),
              border: Border.all(color: Colors.grey.shade200),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.only(left: 16.0),
                    child: TextField(
                      focusNode: _searchFocusNode,
                      decoration: InputDecoration(
                        enabledBorder: InputBorder.none,
                        focusedBorder: InputBorder.none,
                        hintText: 'Search',
                        border: InputBorder.none,
                        hintStyle: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[500],
                        ),
                        isDense: true,
                        contentPadding: EdgeInsets.zero,
                      ),
                    ),
                  ),
                ),
                _MobileSvgButton(
                  iconPath: 'assets/images/search.svg',
                  onPressed: () {},
                  size: 20,
                ),
                Container(
                  height: 24,
                  width: 1,
                  color: Colors.grey.shade200,
                  margin: const EdgeInsets.symmetric(horizontal: 4),
                ),
                _MobileSvgButton(
                  iconPath: 'assets/images/filter-icon.svg',
                  onPressed: () {},
                  size: 24,
                ),
                const SizedBox(width: 8),
              ],
            ),
          ),
          // const SizedBox(height: 12),
        ],
      ),
    );
  }
}

// Mobile SVG Button Widget
class _MobileSvgButton extends StatefulWidget {
  final String iconPath;
  final VoidCallback onPressed;
  final double size;

  const _MobileSvgButton({
    required this.iconPath,
    required this.onPressed,
    this.size = 18,
  });

  @override
  State<_MobileSvgButton> createState() => _MobileSvgButtonState();
}

class _MobileSvgButtonState extends State<_MobileSvgButton> {
  bool isPressed = false;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: (_) => setState(() => isPressed = true),
      onTapUp: (_) => setState(() => isPressed = false),
      onTapCancel: () => setState(() => isPressed = false),
      onTap: widget.onPressed,
      child: Container(
        height: 32,
        width: 32,
        decoration: BoxDecoration(
          border: Border.all(
            color: isPressed ? const Color(0xff0058FF) : Colors.transparent,
            width: 1.0,
          ),
          borderRadius: BorderRadius.circular(4),
        ),
        child: Center(
          child: SvgPicture.asset(
            widget.iconPath,
            width: widget.size,
            height: widget.size,
          ),
        ),
      ),
    );
  }
}
